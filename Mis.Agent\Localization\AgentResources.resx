﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="BarcodeForm" xml:space="preserve">
    <value>Barcode Form</value>
  </data>
  <data name="BarcodeTab.Text" xml:space="preserve">
    <value>Barcode Tab</value>
  </data>
  <data name="PortTabText" xml:space="preserve">
    <value>Port Settings</value>
  </data>
  <data name="AgentUrlLabel" xml:space="preserve">
    <value>Agent Url:</value>
  </data>
  <data name="SavePortConfigButton" xml:space="preserve">
    <value>Save Port Configuration</value>
  </data>
  <data name="BtnSaveAllSettings.Text" xml:space="preserve">
    <value>Save All Settings</value>
  </data>
  <data name="BtnSwitchLanguage.Text" xml:space="preserve">
    <value>Switch Language</value>
  </data>
  <data name="MainFormTitle" xml:space="preserve">
    <value>Agent Form </value>
  </data>
  <data name="PrintTab" xml:space="preserve">
    <value>Print</value>
  </data>
  <data name="TabScanner" xml:space="preserve">
    <value>Scanner</value>
  </data>
  <data name="NotificationsTab" xml:space="preserve">
    <value>Notifications Settings</value>
  </data>
  <data name="PortTab" xml:space="preserve">
    <value>Port</value>
  </data>
  <data name="SwitchToArabic" xml:space="preserve">
    <value>Switch to Arabic</value>
  </data>
  <data name="SwitchToEnglish" xml:space="preserve">
    <value>Switch to English</value>
  </data>
  <data name="LanguageSwitched" xml:space="preserve">
    <value>Language Switched</value>
  </data>
  <data name="LanguageSwitchedMessage" xml:space="preserve">
    <value>Language has been switched successfully.</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ErrorMessage" xml:space="preserve">
    <value>An error occurred</value>
  </data>
  <data name="ConnectionSuccess" xml:space="preserve">
    <value>Connection Successful</value>
  </data>
  <data name="ConnectionSuccessMessage" xml:space="preserve">
    <value>Connection established successfully</value>
  </data>
  <data name="ConnectionFailed" xml:space="preserve">
    <value>Connection Failed</value>
  </data>
  <data name="ConnectionFailedMessage" xml:space="preserve">
    <value>Failed to establish connection</value>
  </data>
  <data name="SettingsSaved" xml:space="preserve">
    <value>Settings Saved</value>
  </data>
  <data name="SettingsSavedMessage" xml:space="preserve">
    <value>Settings have been saved successfully</value>
  </data>
  <data name="BarcodeScanned" xml:space="preserve">
    <value>Barcode Scanned</value>
  </data>
  <data name="BarcodeScannedMessage" xml:space="preserve">
    <value>Barcode has been scanned successfully</value>
  </data>
  <data name="PrintJobCompleted" xml:space="preserve">
    <value>Print Job Completed</value>
  </data>
  <data name="PrintJobCompletedMessage" xml:space="preserve">
    <value>Print job has been completed successfully</value>
  </data>
  <data name="PortConnected" xml:space="preserve">
    <value>Port Connected</value>
  </data>
  <data name="PortConnectedMessage" xml:space="preserve">
    <value>Port connection established successfully</value>
  </data>
  <data name="PortDisconnected" xml:space="preserve">
    <value>Port Disconnected</value>
  </data>
  <data name="PortDisconnectedMessage" xml:space="preserve">
    <value>Port has been disconnected</value>
  </data>
  <data name="Success" xml:space="preserve">
    <value>Success</value>
  </data>
  <data name="Warning" xml:space="preserve">
    <value>Warning</value>
  </data>
  <data name="Information" xml:space="preserve">
    <value>Information</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
    <value>Confirmation</value>
  </data>
  <data name="Yes" xml:space="preserve">
    <value>Yes</value>
  </data>
  <data name="No" xml:space="preserve">
    <value>No</value>
  </data>
  <data name="OK" xml:space="preserve">
    <value>OK</value>
  </data>
  <data name="Cancel" xml:space="preserve">
    <value>Cancel</value>
  </data>
  <data name="Save" xml:space="preserve">
    <value>Save</value>
  </data>
  <data name="Close" xml:space="preserve">
    <value>Close</value>
  </data>
  <data name="BarcodeUrl" xml:space="preserve">
    <value>Barcode URL</value>
  </data>
  <data name="ComPort" xml:space="preserve">
    <value>COM Port</value>
  </data>
  <data name="AvailablePorts" xml:space="preserve">
    <value>Available Ports</value>
  </data>
  <data name="SaveBarcodeConfiguration" xml:space="preserve">
    <value>Save Barcode Configuration</value>
  </data>
  <data name="TestConnection" xml:space="preserve">
    <value>Test Connection</value>
  </data>
  <data name="SendManualData" xml:space="preserve">
    <value>Send Manual Data</value>
  </data>
  <data name="ScannerTab" xml:space="preserve">
    <value>Scanner</value>
  </data>
  <data name="AvailableScanners" xml:space="preserve">
    <value>Available Scanners</value>
  </data>
  <data name="UseBarcodeReader" xml:space="preserve">
    <value>Use Barcode Reader</value>
  </data>
  <data name="SaveScannerConfiguration" xml:space="preserve">
    <value>Save Scanner Configuration</value>
  </data>
  <data name="ScannedImage" xml:space="preserve">
    <value>Scanned Image</value>
  </data>
  <data name="PrinterName" xml:space="preserve">
    <value>Printer Name</value>
  </data>
  <data name="PaperSize" xml:space="preserve">
    <value>Paper Size</value>
  </data>
  <data name="DefaultPrinter" xml:space="preserve">
    <value>Default Printer</value>
  </data>
  <data name="CurrentPrinter" xml:space="preserve">
    <value>Current Printer</value>
  </data>
  <data name="CurrentPaperSize" xml:space="preserve">
    <value>Current Paper Size</value>
  </data>
  <data name="TestPrint" xml:space="preserve">
    <value>Test Print</value>
  </data>
  <data name="SavePrintConfiguration" xml:space="preserve">
    <value>Save Print Configuration</value>
  </data>
  <data name="EnableNotifications" xml:space="preserve">
    <value>Enable/Disable Notifications</value>
  </data>
  <data name="ClearNotifications" xml:space="preserve">
    <value>Clear Notifications</value>
  </data>
  <data name="NotificationId" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="NotificationNumber" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="IsPrinted" xml:space="preserve">
    <value>Is Printed</value>
  </data>
  <data name="ReceiveTime" xml:space="preserve">
    <value>Receive Time</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>An error occurred</value>
  </data>
  <data name="InvalidUrl" xml:space="preserve">
    <value>Invalid URL</value>
  </data>
  <data name="PrinterNotFound" xml:space="preserve">
    <value>Printer not found</value>
  </data>
  <data name="ScannerNotFound" xml:space="preserve">
    <value>Scanner not found</value>
  </data>
  <data name="ConfigurationSaved" xml:space="preserve">
    <value>Configuration saved successfully</value>
  </data>
  <data name="ConfigurationFailed" xml:space="preserve">
    <value>Failed to save configuration</value>
  </data>
  <data name="DatabaseError" xml:space="preserve">
    <value>Database error</value>
  </data>
  <data name="NotificationsSent" xml:space="preserve">
    <value>Notifications sent successfully</value>
  </data>
  <data name="NotificationsCleared" xml:space="preserve">
    <value>All notifications cleared successfully</value>
  </data>
  <data name="ClearNotificationsConfirm" xml:space="preserve">
    <value>Are you sure you want to clear all notifications?</value>
  </data>
  <data name="ClearLogsConfirm" xml:space="preserve">
    <value>Are you sure you want to clear all logs?</value>
  </data>
  <data name="LogsCleared" xml:space="preserve">
    <value>All logs cleared successfully</value>
  </data>
  <data name="ScannerConnected" xml:space="preserve">
    <value>Scanner connected successfully</value>
  </data>
  <data name="ScannerDisconnected" xml:space="preserve">
    <value>Scanner disconnected</value>
  </data>
  <data name="PrintCompleted" xml:space="preserve">
    <value>Print completed successfully</value>
  </data>
  <data name="PrintFailed" xml:space="preserve">
    <value>Print failed</value>
  </data>
  <data name="NoDataToPrint" xml:space="preserve">
    <value>No data to print</value>
  </data>
  <data name="InvalidConfiguration" xml:space="preserve">
    <value>Invalid configuration</value>
  </data>
  <data name="SettingsUpdated" xml:space="preserve">
    <value>Settings updated successfully</value>
  </data>
  <data name="LocalizationTestTitle" xml:space="preserve">
    <value>Localization Test</value>
  </data>
  <data name="WelcomeMessage" xml:space="preserve">
    <value>Welcome to Localization Test</value>
  </data>
  <data name="InstructionsMessage" xml:space="preserve">
    <value>Click the button above to switch between English and Arabic. Notice how the text changes and the layout adjusts for RTL.</value>
  </data>
  <data name="FormTitle" xml:space="preserve">
    <value>MIS Agent</value>
  </data>
  <data name="BarcodeFormTitle" xml:space="preserve">
    <value>Barcode Configuration</value>
  </data>
  <data name="PrintFormTitle" xml:space="preserve">
    <value>Print Configuration</value>
  </data>
  <data name="NotificationsFormTitle" xml:space="preserve">
    <value>Notifications Management</value>
  </data>
  <data name="BarcodeTabText" xml:space="preserve">
    <value>Barcode Settings</value>
  </data>
  <data name="PrintTabText" xml:space="preserve">
    <value>Print Settings</value>
  </data>
  <data name="NotificationsTabText" xml:space="preserve">
    <value>Notifications Settings</value>
  </data>
  <data name="BarcodeUrlLabel" xml:space="preserve">
    <value>Barcode URL:</value>
  </data>
  <data name="ComPortLabel" xml:space="preserve">
    <value>COM Port:</value>
  </data>
  <data name="AvailablePortsLabel" xml:space="preserve">
    <value>Available Ports:</value>
  </data>
  <data name="DefaultPrinterLabel" xml:space="preserve">
    <value>Default Printer:</value>
  </data>
  <data name="PrinterNameLabel" xml:space="preserve">
    <value>Printer Name:</value>
  </data>
  <data name="PaperType" xml:space="preserve">
    <value>Paper Type:</value>
  </data>
  <data name="SaveBarcodeConfigButton" xml:space="preserve">
    <value>Save Barcode Configuration</value>
  </data>
  <data name="TestConnectionButton" xml:space="preserve">
    <value>Test Connection</value>
  </data>
  <data name="SavePrintConfigButton" xml:space="preserve">
    <value>Save Print Configuration</value>
  </data>
  <data name="Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="ClearNotificationsButton" xml:space="preserve">
    <value>Clear All Notifications</value>
  </data>
  <data name="EnableNotificationsCheckbox" xml:space="preserve">
    <value>Enable/Disable Notifications</value>
  </data>
  <data name="PortError" xml:space="preserve">
    <value>Port Error</value>
  </data>
  <data name="PortInUse" xml:space="preserve">
    <value>Port In Use</value>
  </data>
  <data name="InvalidPortFormat" xml:space="preserve">
    <value>Please enter a valid port number between 1 and 65535.</value>
  </data>
  <data name="BarcodeError" xml:space="preserve">
    <value>Barcode Settings Error</value>
  </data>
  <data name="BarcodeUrlMissing" xml:space="preserve">
    <value>Please enter a valid Barcode URL</value>
  </data>
  <data name="BarcodeUrlInvalid" xml:space="preserve">
    <value>Invalid Barcode URL</value>
  </data>
  <data name="ComPortMissing" xml:space="preserve">
    <value>Please select a COM Port</value>
  </data>
  <data name="AgentSuccess" xml:space="preserve">
    <value>Success Process</value>
  </data>
  <data name="RestartRequired" xml:space="preserve">
    <value>Restart Required</value>
  </data>
  <data name="AllSettingsSaved" xml:space="preserve">
    <value>All Settings Saved</value>
  </data>
  <data name="HubConnectionFailed" xml:space="preserve">
    <value>Failed to connect to Hub: No connection could be made because the target machine actively refused it.</value>
  </data>
  <data name="BarcodeSuccess" xml:space="preserve">
    <value>Barcode Success</value>
  </data>
  <data name="HubConnectionSuccess" xml:space="preserve">
    <value>Hub Connection Success</value>
  </data>
  <data name="UpdateNotification" xml:space="preserve">
    <value>Update Notification</value>
  </data>
  <data name="SettingsUpToDate" xml:space="preserve">
    <value>Settings are already up to date.</value>
  </data>
  <data name="NoPortSelectedMessage" xml:space="preserve">
    <value>Please select a COM port.</value>
  </data>
  <data name="UpdateError" xml:space="preserve">
    <value>Update Error</value>
  </data>
  <data name="ErrorSavingSettings" xml:space="preserve">
    <value>Error Saving Settings</value>
  </data>
  <data name="InvalidUrlMessage" xml:space="preserve">
    <value>Please enter a valid Barcode URL.</value>
  </data>
</root>