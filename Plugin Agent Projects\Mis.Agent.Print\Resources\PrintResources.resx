<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="PrintFormTitle" xml:space="preserve">
    <value>Print Configuration</value>
  </data>
  <data name="NotificationsFormTitle" xml:space="preserve">
    <value>Notifications Management</value>
  </data>
  <data name="PrintTabText" xml:space="preserve">
    <value>Print Settings</value>
  </data>
  <data name="NotificationsTabText" xml:space="preserve">
    <value>Notifications</value>
  </data>
  <data name="PrinterNameLabel" xml:space="preserve">
    <value>Printer Name:</value>
  </data>
  <data name="PaperSizeLabel" xml:space="preserve">
    <value>Paper Size:</value>
  </data>
  <data name="DefaultPrinterLabel" xml:space="preserve">
    <value>Default Printer:</value>
  </data>
  <data name="CurrentPrinterLabel" xml:space="preserve">
    <value>Current Printer:</value>
  </data>
  <data name="CurrentPaperSizeLabel" xml:space="preserve">
    <value>Current Paper Size:</value>
  </data>
  <data name="SavePrintConfigButton" xml:space="preserve">
    <value>Save Print Configuration</value>
  </data>
  <data name="TestPrintButton" xml:space="preserve">
    <value>Test Print</value>
  </data>
  <data name="ClearNotificationsButton" xml:space="preserve">
    <value>Clear All Notifications</value>
  </data>
  <data name="EnableNotificationsCheckbox" xml:space="preserve">
    <value>Enable/Disable Notifications</value>
  </data>
  <data name="NotificationIdColumn" xml:space="preserve">
    <value>ID</value>
  </data>
  <data name="NotificationNumberColumn" xml:space="preserve">
    <value>Number</value>
  </data>
  <data name="IsPrintedColumn" xml:space="preserve">
    <value>Is Printed</value>
  </data>
  <data name="ReceiveTimeColumn" xml:space="preserve">
    <value>Receive Time</value>
  </data>
  <data name="PrintConfigSavedMessage" xml:space="preserve">
    <value>Print configuration saved successfully!</value>
  </data>
  <data name="PrintConfigFailedMessage" xml:space="preserve">
    <value>Failed to save print configuration.</value>
  </data>
  <data name="TestPrintSuccessMessage" xml:space="preserve">
    <value>Test print completed successfully!</value>
  </data>
  <data name="TestPrintFailedMessage" xml:space="preserve">
    <value>Test print failed. Please check printer settings.</value>
  </data>
  <data name="NotificationsClearedMessage" xml:space="preserve">
    <value>All notifications cleared successfully!</value>
  </data>
  <data name="ClearNotificationsFailedMessage" xml:space="preserve">
    <value>Failed to clear notifications.</value>
  </data>
  <data name="ClearNotificationsConfirmMessage" xml:space="preserve">
    <value>Are you sure you want to clear all notifications?</value>
  </data>
  <data name="NotificationsEnabledMessage" xml:space="preserve">
    <value>Notifications enabled.</value>
  </data>
  <data name="NotificationsDisabledMessage" xml:space="preserve">
    <value>Notifications disabled.</value>
  </data>
  <data name="NoPrinterSelectedMessage" xml:space="preserve">
    <value>Please select a printer.</value>
  </data>
  <data name="PrinterNotFoundMessage" xml:space="preserve">
    <value>Selected printer not found.</value>
  </data>
  <data name="Properties" xml:space="preserve">
    <value>Properties</value>
  </data>
  <data name="PaperType" xml:space="preserve">
    <value>Paper Type</value>
  </data>
  <data name="PrintingError" xml:space="preserve">
    <value>Printing Error</value>
  </data>
  <data name="HTMLContent" xml:space="preserve">
    <value>HTML content cannot be null or empty.</value>
  </data>
  <data name="PrintError" xml:space="preserve">
    <value>Print Error</value>
  </data>
  <data name="NoPrinterSelected" xml:space="preserve">
    <value>No printer selected</value>
  </data>
  <data name="PaperSizeNotAvailable" xml:space="preserve">
    <value>Selected paper size is not available for the selected printer</value>
  </data>
  <data name="NoPaperSizeSelected" xml:space="preserve">
    <value>No paper size selected</value>
  </data>
  <data name="PrinterPropertiesFailed" xml:space="preserve">
    <value>Failed to show printer properties</value>
  </data>
  <data name="PrinterPropertiesError" xml:space="preserve">
    <value>Error showing printer properties</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>Error</value>
  </data>
  <data name="ErrorOccurred" xml:space="preserve">
    <value>Error Occurred</value>
  </data>
</root>