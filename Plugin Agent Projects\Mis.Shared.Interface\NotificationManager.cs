﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing;
using System.Globalization;

namespace Mis.Shared.Interface
{
    public delegate void NotificationEventHandler(object sender, NotificationEventArgs e);

    public class NotificationEventArgs : EventArgs
    {
        public bool IsEnabled { get; set; }
    }

    public class CultureChangedEventArgs : EventArgs
    {
        public CultureInfo? OldCulture { get; set; }
        public CultureInfo? NewCulture { get; set; }
        public bool IsRightToLeft { get; set; }
    }

    public delegate void NotificationDataRequestHandler(object sender, NotificationDataEventArgs e);

    public class NotificationDataEventArgs : EventArgs
    {
        public IEnumerable<TransactionDto>? Notifications { get; set; }
        public TaskCompletionSource<IEnumerable<TransactionDto>>? CompletionSource { get; set; }
    }

    public class ImageCapturedEventArgs : EventArgs
    {
        public Image? CapturedImage { get; set; }
        public string? ImageType { get; set; } // "Barcode" or "Scanner"
    }

    public static class NotificationManager
    {
        public static event NotificationEventHandler? NotificationEvent;
        public static event Action? NotificationDataUpdated;
        public static event Action? NotificationCleanupRequested;
        public static event NotificationDataRequestHandler? NotificationDataRequested;

        // Image capture events for barcode and scanner
        public static event Action<Image>? BarcodeImageCaptured;
        public static event Action<Image>? ScannerImageCaptured;

        // Localization delegate for getting localized strings
        public static Func<string, string, string>? GetLocalizedString { get; set; }

        // Notification enabled state
        private static bool _notificationsEnabled = true;

        public static void Notify(NotificationEventArgs e)
        {
            NotificationEvent?.Invoke(null, e);
        }

        public static void NotifyDataUpdated()
        {
            NotificationDataUpdated?.Invoke();
        }

        public static void RequestCleanup()
        {
            NotificationCleanupRequested?.Invoke();
        }

        public static async Task<IEnumerable<TransactionDto>> RequestNotificationDataAsync()
        {
            var completionSource = new TaskCompletionSource<IEnumerable<TransactionDto>>();
            var eventArgs = new NotificationDataEventArgs
            {
                CompletionSource = completionSource
            };

            NotificationDataRequested?.Invoke(null, eventArgs);

            // Wait for the response with a timeout
            var timeoutTask = Task.Delay(5000); // 5 second timeout
            var completedTask = await Task.WhenAny(completionSource.Task, timeoutTask);

            if (completedTask == timeoutTask)
            {
                return new List<TransactionDto>(); // Return empty list on timeout
            }

            return await completionSource.Task;
        }

        // Image capture notification methods
        public static void NotifyBarcodeImageCaptured(Image image)
        {
            BarcodeImageCaptured?.Invoke(image);
        }

        public static void NotifyScannerImageCaptured(Image image)
        {
            ScannerImageCaptured?.Invoke(image);
        }

        // Centralized localized notification method
        public static void ShowNotification(string titleKey, string messageKey, string defaultTitle = null, string defaultMessage = null)
        {
            if (!_notificationsEnabled) return;

            try
            {
                // Get localized strings using the delegate if available
                string title = GetLocalizedString?.Invoke(titleKey, defaultTitle ?? titleKey) ?? defaultTitle ?? titleKey;
                string message = GetLocalizedString?.Invoke(messageKey, defaultMessage ?? messageKey) ?? defaultMessage ?? messageKey;

                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = message
                })
                {
                    notifyIcon.ShowBalloonTip(3000);
                    Task.Delay(3000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
            catch (Exception ex)
            {
                // Fallback to console output if notification fails
                Console.WriteLine($"Notification error: {ex.Message}");
            }
        }

        // Overload for direct text (for backward compatibility)
        public static void ShowNotification(string title, string message)
        {
            if (!_notificationsEnabled) return;

            try
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = message
                })
                {
                    notifyIcon.ShowBalloonTip(3000);
                    Task.Delay(3000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Notification error: {ex.Message}");
            }
        }

        // Method to set notification enabled state
        public static void SetNotificationEnabled(bool enabled)
        {
            _notificationsEnabled = enabled;
        }
    }


}
