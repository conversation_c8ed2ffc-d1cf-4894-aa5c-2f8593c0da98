using Mis.Shared.Interface;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json.Linq;
using System;
using System.Collections.Generic;
using System.IO.Ports;
using System.Linq;
using System.Management;
using System.Text;
using System.Threading.Tasks;
using WIA;
using CommonDialog = WIA.CommonDialog;
using System.Drawing;
using System.IO;
using System.Windows.Forms;

namespace Mis.Agent.Barcode
{
    [Plugin("Scanner", Version = "1.0.0", Description = "Scanner configuration and management", Order = 50)]
    public class ScannerService : IScannerAppService
    {
        private readonly IConfiguration _configuration;
        private readonly string _filePath;
        public event Action<Image> scannerImageCaptured; // Define an event
        public event Action<bool> NotificationStateChanged;
        bool _notificationsEnabled;


        ScannerForm scannerForm;

        public ScannerService()
        {
            _filePath = Path.Combine(AppContext.BaseDirectory, "appsettings.json");

            if (!File.Exists(_filePath))
            {
                MessageBox.Show($"Missing configuration file: {_filePath}", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Environment.Exit(1);
            }

            _configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .Build();
            NotificationManager.NotificationEvent += OnNotificationReceived;
            GetNotificationEnabledSetting();
            scannerForm = new ScannerForm(this);

        }
        public void GetNotificationEnabledSetting()
        {
            var section = _configuration.GetSection("NotificationSettings");
            _notificationsEnabled = section.GetValue<bool?>("EnabeledNotification") ?? false;
        }
        private void OnNotificationReceived(object sender, NotificationEventArgs e)
        {
            _notificationsEnabled = e.IsEnabled; // Additional logic based on notification state
        }
        [PluginNotificationHandler]
        public void ShowNotification(string title, string text)
        {
            if (_notificationsEnabled)
            {
                using (var notifyIcon = new NotifyIcon
                {
                    Icon = SystemIcons.Information,
                    Visible = true,
                    BalloonTipTitle = title,
                    BalloonTipText = text
                })
                {
                    notifyIcon.ShowBalloonTip(1000);
                    Task.Delay(1000).ContinueWith(t => notifyIcon.Dispose());
                }
            }
        }











        // Method to retrieve the current settings
        public string GetSetting(string key)
        {
            // Access the DefaultScanner section
            var section = _configuration.GetSection("DefaultScanner");

            if (section.GetValue<string>(key) != null)
            {
                return section.GetValue<string>(key);
            }

            throw new KeyNotFoundException($"Setting with key '{key}' not found.");
        }


        // Method to get available scanners on the machine
        public List<string> GetAvailableScanners()
        {
            var scanners = new List<string>();

            try
            {
                // WMI query to get all PnP devices
                var searcher = new ManagementObjectSearcher(@"SELECT * FROM Win32_PnPEntity WHERE PNPClass = 'Image' OR PNPClass = 'Camera'");

                foreach (ManagementObject device in searcher.Get())
                {
                    // Check if "Caption" is not null and add all devices under 'Image' or 'Camera' classes
                    if (device["Caption"] != null)
                    {
                        string deviceName = device["Caption"].ToString();
                        scanners.Add(deviceName);
                    }
                }

                // Remove duplicates, if any
                scanners = scanners.Distinct().ToList();
            }
            catch (Exception ex)
            {
                Console.WriteLine("Error retrieving scanners: " + ex.Message);
            }

            return scanners;
        }



        [PluginTabProvider]
        public object GetTabPage()
        {
            return scannerForm.ScannerTab;
        }

        [PluginConfiguration("BaseUrl")]
        public string GetBaseUrl()
        {
            return _configuration.GetSection("Server").GetValue<string>("BaseUrl");

        }
        [PluginConfiguration("ComPort")]

        public string GetCOMPort()
        {
            return _configuration.GetSection("Barcode").GetValue<string>("ComPort");
        }
        [PluginConfiguration("BarcodeBaseUrl")]
        public string GetBarcodeBaseUrl()
        {
            return _configuration.GetSection("Barcode").GetValue<string>("BarcodeBaseUrl");
        }

        #region Scanner Methods (Moved from SerialPortManager)

        public string ScanWithSelectedScanner(string scannerName)
        {
            if (string.IsNullOrWhiteSpace(scannerName))
            {
                throw new ArgumentException("Scanner name cannot be null or empty.", nameof(scannerName));
            }

            try
            {
                // Create a new WIA Device Manager
                var deviceManager = new DeviceManager();

                // Find the scanner device by name
                DeviceInfo selectedDevice = null;
                foreach (DeviceInfo device in deviceManager.DeviceInfos)
                {
                    if (device.Type == WiaDeviceType.ScannerDeviceType)
                    {
                        // Check if this is the scanner we want based on the scannerName
                        string deviceName = device.Properties["Name"].get_Value().ToString();
                        if (deviceName.Equals(scannerName, StringComparison.OrdinalIgnoreCase))
                        {
                            selectedDevice = device;
                            break;
                        }
                    }
                }

                // Throw an exception if the scanner was not found
                if (selectedDevice == null)
                {
                    throw new Exception($"Scanner '{scannerName}' not found.");
                }

                // Connect to the selected device
                var scanner = selectedDevice.Connect();

                // Select the scanner's flatbed (if applicable)
                var item = scanner.Items[1];

                // Perform the scan
                var commonDialog = new CommonDialog();
                ImageFile imageFile = (ImageFile)commonDialog.ShowTransfer(item, FormatID.wiaFormatJPEG, false);

                // Save the image to a temporary file
                var tempFilePath = Path.Combine(Path.GetTempPath(), $"{Guid.NewGuid()}.jpg");
                imageFile.SaveFile(tempFilePath);

                // Convert the image to a base64 string
                byte[] imageBytes = File.ReadAllBytes(tempFilePath);
                string base64Image = Convert.ToBase64String(imageBytes);

                // Clean up the temporary file
                File.Delete(tempFilePath);
                if (base64Image != null)
                {
                    // Convert byte array directly to Image for display
                    using (var ms = new MemoryStream(imageBytes))
                    {
                        var img = Image.FromStream(ms);
                        if (img != null)
                        {
                            // Use NotificationManager instead of direct event
                            NotificationManager.NotifyScannerImageCaptured(img);
                        }
                    }
                }
                // Return the base64 representation of the scanned image
                return base64Image;
            }
            catch (Exception ex)
            {
                // Handle errors (e.g., scanner not found, scan failure)
                throw new Exception($"Error during scanning: {ex.Message}", ex);
            }
        }

        #endregion

    }
}
