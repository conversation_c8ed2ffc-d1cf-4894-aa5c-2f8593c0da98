﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace Mis.Agent.Print.Resources {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class PrintResources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal PrintResources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("Mis.Agent.Print.Resources.PrintResources", typeof(PrintResources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Clear All Notifications.
        /// </summary>
        internal static string ClearNotificationsButton {
            get {
                return ResourceManager.GetString("ClearNotificationsButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Are you sure you want to clear all notifications?.
        /// </summary>
        internal static string ClearNotificationsConfirmMessage {
            get {
                return ResourceManager.GetString("ClearNotificationsConfirmMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to clear notifications..
        /// </summary>
        internal static string ClearNotificationsFailedMessage {
            get {
                return ResourceManager.GetString("ClearNotificationsFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Paper Size:.
        /// </summary>
        internal static string CurrentPaperSizeLabel {
            get {
                return ResourceManager.GetString("CurrentPaperSizeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Current Printer:.
        /// </summary>
        internal static string CurrentPrinterLabel {
            get {
                return ResourceManager.GetString("CurrentPrinterLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Default Printer:.
        /// </summary>
        internal static string DefaultPrinterLabel {
            get {
                return ResourceManager.GetString("DefaultPrinterLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Enable/Disable Notifications.
        /// </summary>
        internal static string EnableNotificationsCheckbox {
            get {
                return ResourceManager.GetString("EnableNotificationsCheckbox", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error.
        /// </summary>
        internal static string Error {
            get {
                return ResourceManager.GetString("Error", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error Occurred.
        /// </summary>
        internal static string ErrorOccurred {
            get {
                return ResourceManager.GetString("ErrorOccurred", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to HTML content cannot be null or empty..
        /// </summary>
        internal static string HTMLContent {
            get {
                return ResourceManager.GetString("HTMLContent", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Is Printed.
        /// </summary>
        internal static string IsPrintedColumn {
            get {
                return ResourceManager.GetString("IsPrintedColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No paper size selected.
        /// </summary>
        internal static string NoPaperSizeSelected {
            get {
                return ResourceManager.GetString("NoPaperSizeSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to No printer selected.
        /// </summary>
        internal static string NoPrinterSelected {
            get {
                return ResourceManager.GetString("NoPrinterSelected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Please select a printer..
        /// </summary>
        internal static string NoPrinterSelectedMessage {
            get {
                return ResourceManager.GetString("NoPrinterSelectedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to ID.
        /// </summary>
        internal static string NotificationIdColumn {
            get {
                return ResourceManager.GetString("NotificationIdColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Number.
        /// </summary>
        internal static string NotificationNumberColumn {
            get {
                return ResourceManager.GetString("NotificationNumberColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to All notifications cleared successfully!.
        /// </summary>
        internal static string NotificationsClearedMessage {
            get {
                return ResourceManager.GetString("NotificationsClearedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications disabled..
        /// </summary>
        internal static string NotificationsDisabledMessage {
            get {
                return ResourceManager.GetString("NotificationsDisabledMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications enabled..
        /// </summary>
        internal static string NotificationsEnabledMessage {
            get {
                return ResourceManager.GetString("NotificationsEnabledMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications Management.
        /// </summary>
        internal static string NotificationsFormTitle {
            get {
                return ResourceManager.GetString("NotificationsFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Notifications.
        /// </summary>
        internal static string NotificationsTabText {
            get {
                return ResourceManager.GetString("NotificationsTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paper Size:.
        /// </summary>
        internal static string PaperSizeLabel {
            get {
                return ResourceManager.GetString("PaperSizeLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected paper size is not available for the selected printer.
        /// </summary>
        internal static string PaperSizeNotAvailable {
            get {
                return ResourceManager.GetString("PaperSizeNotAvailable", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Paper Type.
        /// </summary>
        internal static string PaperType {
            get {
                return ResourceManager.GetString("PaperType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to save print configuration..
        /// </summary>
        internal static string PrintConfigFailedMessage {
            get {
                return ResourceManager.GetString("PrintConfigFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print configuration saved successfully!.
        /// </summary>
        internal static string PrintConfigSavedMessage {
            get {
                return ResourceManager.GetString("PrintConfigSavedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printer Name:.
        /// </summary>
        internal static string PrinterNameLabel {
            get {
                return ResourceManager.GetString("PrinterNameLabel", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Selected printer not found..
        /// </summary>
        internal static string PrinterNotFoundMessage {
            get {
                return ResourceManager.GetString("PrinterNotFoundMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Error showing printer properties.
        /// </summary>
        internal static string PrinterPropertiesError {
            get {
                return ResourceManager.GetString("PrinterPropertiesError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Failed to show printer properties.
        /// </summary>
        internal static string PrinterPropertiesFailed {
            get {
                return ResourceManager.GetString("PrinterPropertiesFailed", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Error.
        /// </summary>
        internal static string PrintError {
            get {
                return ResourceManager.GetString("PrintError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Configuration.
        /// </summary>
        internal static string PrintFormTitle {
            get {
                return ResourceManager.GetString("PrintFormTitle", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Printing Error.
        /// </summary>
        internal static string PrintingError {
            get {
                return ResourceManager.GetString("PrintingError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Print Settings.
        /// </summary>
        internal static string PrintTabText {
            get {
                return ResourceManager.GetString("PrintTabText", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Properties.
        /// </summary>
        internal static string Properties {
            get {
                return ResourceManager.GetString("Properties", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Receive Time.
        /// </summary>
        internal static string ReceiveTimeColumn {
            get {
                return ResourceManager.GetString("ReceiveTimeColumn", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Save Print Configuration.
        /// </summary>
        internal static string SavePrintConfigButton {
            get {
                return ResourceManager.GetString("SavePrintConfigButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test Print.
        /// </summary>
        internal static string TestPrintButton {
            get {
                return ResourceManager.GetString("TestPrintButton", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test print failed. Please check printer settings..
        /// </summary>
        internal static string TestPrintFailedMessage {
            get {
                return ResourceManager.GetString("TestPrintFailedMessage", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to Test print completed successfully!.
        /// </summary>
        internal static string TestPrintSuccessMessage {
            get {
                return ResourceManager.GetString("TestPrintSuccessMessage", resourceCulture);
            }
        }
    }
}
