2025-07-18 00:01:28.636 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:01:28.684 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:01:28.694 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:01:28.705 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:01:28.710 +03:00 [INF] Initializing hub connection...
2025-07-18 00:01:30.463 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:01:30.536 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:01:30.542 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:01:30.549 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:01:30.552 +03:00 [INF] Hosting environment: Production
2025-07-18 00:01:30.556 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:01:31.112 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:01:31.154 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:01:31.181 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:01:31.185 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 79.0297ms
2025-07-18 00:01:33.383 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=GTLn4iP8yP4sIo1lSW2vcA - null null
2025-07-18 00:01:33.402 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:01:33.504 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:01:33.563 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:02:11.353 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:02:11.413 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:02:11.424 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:02:11.437 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:02:11.442 +03:00 [INF] Initializing hub connection...
2025-07-18 00:02:12.998 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:02:13.067 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:02:13.069 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:02:13.072 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:02:13.073 +03:00 [INF] Hosting environment: Production
2025-07-18 00:02:13.075 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:02:13.825 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:02:13.872 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:02:13.966 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:02:13.972 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 155.489ms
2025-07-18 00:02:16.123 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=olrLTVCsmK9EwPoHieGHxQ - null null
2025-07-18 00:02:16.140 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:02:16.232 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:02:16.300 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:03:37.228 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:03:37.235 +03:00 [INF] COM ports populated.
2025-07-18 00:03:37.241 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:03:37.249 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:04:17.233 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:04:17.265 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:04:17.268 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:04:17.272 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:04:17.273 +03:00 [INF] Initializing hub connection...
2025-07-18 00:04:18.950 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:04:19.048 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:04:19.054 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:04:19.060 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:04:19.062 +03:00 [INF] Hosting environment: Production
2025-07-18 00:04:19.063 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:04:19.694 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:04:19.746 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:04:19.776 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:04:19.786 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 98.4484ms
2025-07-18 00:04:21.939 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Yj7nst_4D91cQkKDw9UAog - null null
2025-07-18 00:04:21.948 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:04:22.065 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:04:22.132 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:04:26.870 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:04:26.875 +03:00 [INF] COM ports populated.
2025-07-18 00:04:26.881 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:04:26.885 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:06:23.576 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:06:23.613 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:06:23.617 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:06:23.620 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:06:23.621 +03:00 [INF] Initializing hub connection...
2025-07-18 00:06:25.204 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:06:25.267 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:06:25.269 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:06:25.272 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:06:25.273 +03:00 [INF] Hosting environment: Production
2025-07-18 00:06:25.274 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:06:25.977 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:06:26.017 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:06:26.044 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:06:26.052 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 85.5762ms
2025-07-18 00:06:26.438 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:06:26.455 +03:00 [INF] COM ports populated.
2025-07-18 00:06:26.462 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:06:26.468 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:08:43.111 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=R0ZxTeFi7Wk7CeJMlJVhVw - null null
2025-07-18 00:08:45.571 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:08:47.176 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:08:47.227 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:13:19.617 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:13:19.663 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:13:19.672 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:13:19.683 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:13:19.689 +03:00 [INF] Initializing hub connection...
2025-07-18 00:13:21.295 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:13:21.381 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:13:21.387 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:13:21.396 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:13:21.400 +03:00 [INF] Hosting environment: Production
2025-07-18 00:13:21.405 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:13:22.067 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:13:22.102 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:13:22.121 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:13:22.152 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 89.7499ms
2025-07-18 00:13:24.258 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=EDNkpt76M434oiysEtVHjQ - null null
2025-07-18 00:13:24.277 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:13:24.364 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:13:24.414 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:13:37.036 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:13:37.042 +03:00 [INF] COM ports populated.
2025-07-18 00:13:37.048 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:13:37.052 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:15:58.434 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:15:58.476 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:15:58.479 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:15:58.482 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:15:58.483 +03:00 [INF] Initializing hub connection...
2025-07-18 00:16:00.080 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:16:00.161 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:16:00.166 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:16:00.171 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:16:00.173 +03:00 [INF] Hosting environment: Production
2025-07-18 00:16:00.174 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:16:00.885 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:16:00.923 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:16:00.947 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:16:00.949 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 69.6864ms
2025-07-18 00:16:03.096 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=yLOMjtwTahrfCF1JI2CvhQ - null null
2025-07-18 00:16:03.113 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:16:03.216 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:16:03.267 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:16:08.325 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:16:08.330 +03:00 [INF] COM ports populated.
2025-07-18 00:16:08.337 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:16:08.344 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:17:50.658 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:17:50.697 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:17:50.704 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:17:50.710 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:17:50.712 +03:00 [INF] Initializing hub connection...
2025-07-18 00:17:52.203 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:17:52.294 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:17:52.300 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:17:52.310 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:17:52.314 +03:00 [INF] Hosting environment: Production
2025-07-18 00:17:52.318 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:17:53.098 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:17:53.137 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:17:53.163 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:17:53.172 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.412ms
2025-07-18 00:17:55.328 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=Q9JGgowf8-IOwdk5fqHLdA - null null
2025-07-18 00:17:55.340 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:17:55.412 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:17:55.464 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:18:02.098 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:18:02.103 +03:00 [INF] COM ports populated.
2025-07-18 00:18:02.107 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:18:02.112 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:28:11.444 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:28:11.481 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:28:11.485 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:28:11.488 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:28:11.488 +03:00 [INF] Initializing hub connection...
2025-07-18 00:28:12.967 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:28:13.045 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:28:13.050 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:28:13.058 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:28:13.063 +03:00 [INF] Hosting environment: Production
2025-07-18 00:28:13.069 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:28:13.828 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:28:13.872 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:28:13.903 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:28:13.912 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 88.6295ms
2025-07-18 00:28:16.051 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=MlCabjAp-IFhMy0aJyCM9A - null null
2025-07-18 00:28:16.066 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:28:16.168 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:28:16.244 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:32:10.661 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:32:10.665 +03:00 [INF] COM ports populated.
2025-07-18 00:32:10.670 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:32:10.674 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:33:00.637 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-18 00:33:00.646 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=MlCabjAp-IFhMy0aJyCM9A - 101 null null 284600.0616ms
2025-07-18 00:33:05.721 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:33:05.738 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:33:05.741 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:33:05.743 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 22.2287ms
2025-07-18 00:33:07.842 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=ba0RiAjY_eZUdVHbLWbvAw - null null
2025-07-18 00:33:07.861 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:34:58.395 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:34:58.427 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:34:58.430 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:34:58.433 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:34:58.434 +03:00 [INF] Initializing hub connection...
2025-07-18 00:34:59.946 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:35:00.035 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:35:00.042 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:35:00.051 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:35:00.056 +03:00 [INF] Hosting environment: Production
2025-07-18 00:35:00.061 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:35:00.830 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:35:00.865 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:35:00.889 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:35:00.898 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 71.9863ms
2025-07-18 00:35:03.118 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=5yL5mujpGDqlMGTIlT96CA - null null
2025-07-18 00:35:03.133 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:35:03.211 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:35:03.235 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:35:24.563 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:35:24.568 +03:00 [INF] COM ports populated.
2025-07-18 00:35:24.572 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:35:24.575 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:37:17.794 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:37:17.846 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:37:17.850 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:37:17.853 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:37:17.853 +03:00 [INF] Initializing hub connection...
2025-07-18 00:37:19.342 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:37:19.440 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:37:19.445 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:37:19.450 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:37:19.455 +03:00 [INF] Hosting environment: Production
2025-07-18 00:37:19.461 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:37:20.164 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:37:20.194 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:37:20.214 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:37:20.217 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 58.1009ms
2025-07-18 00:37:22.368 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=L97WXYQObwO7-fE8ke3oLg - null null
2025-07-18 00:37:22.376 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:37:22.473 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:37:22.499 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:37:23.867 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:37:23.872 +03:00 [INF] COM ports populated.
2025-07-18 00:37:23.878 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:37:23.883 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:41:27.970 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:41:28.016 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:41:28.023 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:41:28.028 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:41:28.029 +03:00 [INF] Initializing hub connection...
2025-07-18 00:41:29.468 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:41:29.545 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:41:29.548 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:41:29.553 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:41:29.557 +03:00 [INF] Hosting environment: Production
2025-07-18 00:41:29.558 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:41:30.355 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:41:30.395 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:41:30.424 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:41:30.432 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 84.2311ms
2025-07-18 00:41:32.611 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=0ILSs6HOydHe6nyeCh7Zww - null null
2025-07-18 00:41:32.629 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:41:32.717 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:41:32.742 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:41:43.862 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:41:43.864 +03:00 [INF] COM ports populated.
2025-07-18 00:41:43.866 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:41:43.867 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:46:37.140 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:46:37.183 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:46:37.191 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:46:37.201 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:46:37.205 +03:00 [INF] Initializing hub connection...
2025-07-18 00:46:38.799 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:46:38.882 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:46:38.887 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:46:38.894 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:46:38.899 +03:00 [INF] Hosting environment: Production
2025-07-18 00:46:38.904 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:46:39.665 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:46:39.700 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:46:39.720 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:46:39.724 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 65.9616ms
2025-07-18 00:46:41.849 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=bHCseifq41W73R-yaoZJ7g - null null
2025-07-18 00:46:41.866 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:46:41.969 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:46:42.001 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:46:42.518 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:46:42.523 +03:00 [INF] COM ports populated.
2025-07-18 00:46:42.530 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:46:42.536 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:51:16.139 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:51:16.186 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:51:16.196 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:51:16.208 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:51:16.214 +03:00 [INF] Initializing hub connection...
2025-07-18 00:51:17.768 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:51:17.848 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:51:17.854 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:51:17.860 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:51:17.862 +03:00 [INF] Hosting environment: Production
2025-07-18 00:51:17.864 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:51:18.619 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:51:18.658 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:51:18.689 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:51:18.710 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 97.9818ms
2025-07-18 00:51:20.895 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=DFmEYdlUnMLM06FWp36Olw - null null
2025-07-18 00:51:20.913 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:51:21.005 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:51:21.034 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:51:27.692 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:51:27.698 +03:00 [INF] COM ports populated.
2025-07-18 00:51:27.704 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:51:27.711 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:52:08.186 +03:00 [INF] Executed endpoint '/chatHub'
2025-07-18 00:52:08.190 +03:00 [INF] Request finished HTTP/1.1 GET http://localhost:5000/chatHub?id=DFmEYdlUnMLM06FWp36Olw - 101 null null 47295.923ms
2025-07-18 00:53:41.402 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:53:41.456 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:53:41.465 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:53:41.477 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:53:41.479 +03:00 [INF] Initializing hub connection...
2025-07-18 00:53:43.078 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:53:43.151 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:53:43.157 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:53:43.164 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:53:43.169 +03:00 [INF] Hosting environment: Production
2025-07-18 00:53:43.174 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:53:43.852 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:53:43.882 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:53:43.902 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:53:43.905 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 57.4133ms
2025-07-18 00:53:46.028 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=vaLlAK-TGBlNTShJDSm6kA - null null
2025-07-18 00:53:46.043 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:53:46.145 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:53:46.173 +03:00 [INF] Barcode initialized successfully.
2025-07-18 00:53:54.385 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 00:53:54.391 +03:00 [INF] COM ports populated.
2025-07-18 00:53:54.397 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 00:53:54.402 +03:00 [INF] TabPage returned successfully.
2025-07-18 00:55:09.386 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 00:55:09.442 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 00:55:09.449 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 00:55:10.001 +03:00 [INF] Capture image mode set to: false
2025-07-18 00:55:10.004 +03:00 [INF] Initializing hub connection...
2025-07-18 00:55:15.227 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 00:55:15.317 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 00:55:15.322 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 00:55:15.329 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 00:55:15.334 +03:00 [INF] Hosting environment: Production
2025-07-18 00:55:15.340 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 00:55:15.536 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 00:55:15.575 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 00:55:15.606 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 00:55:15.611 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 80.4471ms
2025-07-18 00:55:17.787 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=_pz9w75jzZGWvKKu0nj5ew - null null
2025-07-18 00:55:17.795 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 00:55:17.974 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 00:55:18.040 +03:00 [INF] Barcode initialized successfully.
2025-07-18 01:06:01.559 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 01:06:01.598 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 01:06:01.602 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 01:06:01.606 +03:00 [INF] Capture image mode set to: false
2025-07-18 01:06:01.607 +03:00 [INF] Initializing hub connection...
2025-07-18 01:06:10.164 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 01:06:10.233 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 01:06:10.238 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 01:06:10.246 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 01:06:10.249 +03:00 [INF] Hosting environment: Production
2025-07-18 01:06:10.253 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 01:06:10.442 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 01:06:10.479 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 01:06:10.503 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 01:06:10.506 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 69.2871ms
2025-07-18 01:06:12.659 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=2LW2TMJhyD8Smip5BlFBlQ - null null
2025-07-18 01:06:12.678 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 01:06:12.772 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 01:06:12.799 +03:00 [INF] Barcode initialized successfully.
2025-07-18 01:09:49.363 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 01:09:49.407 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 01:09:49.413 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 01:09:49.416 +03:00 [INF] Capture image mode set to: false
2025-07-18 01:09:49.418 +03:00 [INF] Initializing hub connection...
2025-07-18 01:09:54.629 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 01:09:54.714 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 01:09:54.719 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 01:09:54.727 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 01:09:54.732 +03:00 [INF] Hosting environment: Production
2025-07-18 01:09:54.739 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 01:09:54.912 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 01:09:54.963 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 01:09:54.993 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 01:09:55.000 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 95.4389ms
2025-07-18 01:09:57.213 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=qOGZUHhAHCHLAh-PxWqcUw - null null
2025-07-18 01:09:57.232 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 01:09:57.323 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 01:09:57.355 +03:00 [INF] Barcode initialized successfully.
2025-07-18 01:09:58.706 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 01:09:58.712 +03:00 [INF] COM ports populated.
2025-07-18 01:09:58.720 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 01:09:58.727 +03:00 [INF] TabPage returned successfully.
2025-07-18 01:29:44.627 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 01:29:44.668 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 01:29:44.675 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 01:29:44.677 +03:00 [INF] Capture image mode set to: false
2025-07-18 01:29:44.678 +03:00 [INF] Initializing hub connection...
2025-07-18 01:29:46.221 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 01:29:46.302 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 01:29:46.305 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 01:29:46.314 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 01:29:46.320 +03:00 [INF] Hosting environment: Production
2025-07-18 01:29:46.325 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 01:29:47.074 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 01:29:47.124 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 01:29:47.156 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 01:29:47.165 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 96.4357ms
2025-07-18 01:29:49.354 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=zYnVbkcvaFmUqnKg1Muyqg - null null
2025-07-18 01:29:49.372 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 01:29:49.474 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 01:29:49.505 +03:00 [INF] Barcode initialized successfully.
2025-07-18 01:29:54.904 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 01:29:54.909 +03:00 [INF] COM ports populated.
2025-07-18 01:29:54.915 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 01:29:54.920 +03:00 [INF] TabPage returned successfully.
2025-07-18 01:30:11.319 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/ - null null
2025-07-18 01:30:11.351 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/ - 301 0 null 31.3415ms
2025-07-18 01:30:11.627 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-18 01:30:11.706 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 79.3997ms
2025-07-18 01:30:11.999 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-18 01:30:11.999 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-18 01:30:12.020 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-18 01:30:12.053 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-18 01:30:12.061 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-18 01:30:12.020 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-18 01:30:12.099 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 78.3679ms
2025-07-18 01:30:12.103 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 103.3936ms
2025-07-18 01:30:12.125 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-18 01:30:12.127 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-18 01:30:12.147 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 148.1751ms
2025-07-18 01:30:12.149 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 128.7353ms
2025-07-18 01:30:12.162 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-18 01:30:12.170 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 150.9341ms
2025-07-18 01:30:12.760 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-18 01:30:12.886 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 126.6143ms
2025-07-18 01:30:25.670 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 161
2025-07-18 01:30:25.677 +03:00 [INF] CORS policy execution successful.
2025-07-18 01:30:25.683 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 01:30:25.712 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 01:30:26.099 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 02:26:12.251 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 02:26:12.293 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 02:26:12.297 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 02:26:12.304 +03:00 [INF] Capture image mode set to: false
2025-07-18 02:26:12.306 +03:00 [INF] Initializing hub connection...
2025-07-18 02:26:13.999 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 02:26:14.088 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 02:26:14.094 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 02:26:14.102 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 02:26:14.107 +03:00 [INF] Hosting environment: Production
2025-07-18 02:26:14.111 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 02:26:14.796 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 02:26:14.835 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 02:26:14.862 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 02:26:14.903 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 82.2189ms
2025-07-18 02:26:16.997 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=yvFOoI42qKQbWlcfu0JphQ - null null
2025-07-18 02:26:17.011 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 02:26:17.180 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 02:26:17.215 +03:00 [INF] Barcode initialized successfully.
2025-07-18 02:26:38.695 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 02:26:38.700 +03:00 [INF] COM ports populated.
2025-07-18 02:26:38.706 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 02:26:38.712 +03:00 [INF] TabPage returned successfully.
2025-07-18 14:55:05.742 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 14:55:05.818 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 14:55:05.824 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 14:55:05.835 +03:00 [INF] Capture image mode set to: false
2025-07-18 14:55:05.841 +03:00 [INF] Initializing hub connection...
2025-07-18 14:55:09.602 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 14:55:09.714 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 14:55:09.719 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 14:55:09.726 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 14:55:09.732 +03:00 [INF] Hosting environment: Production
2025-07-18 14:55:09.738 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 14:55:09.940 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 14:55:10.004 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 14:55:10.049 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 14:55:10.054 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 120.3476ms
2025-07-18 14:55:12.273 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=A4wg6vvK-LNgDTR8_KkS6A - null null
2025-07-18 14:55:12.291 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 14:55:12.377 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 14:55:12.437 +03:00 [INF] Barcode initialized successfully.
2025-07-18 14:55:30.984 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 14:55:30.989 +03:00 [INF] COM ports populated.
2025-07-18 14:55:30.995 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 14:55:30.999 +03:00 [INF] TabPage returned successfully.
2025-07-18 14:55:58.907 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-18 14:55:58.971 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 64.4556ms
2025-07-18 14:55:59.044 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-18 14:55:59.044 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-18 14:55:59.044 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-18 14:55:59.044 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-18 14:55:59.044 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-18 14:55:59.065 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-18 14:55:59.066 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 21.5747ms
2025-07-18 14:55:59.068 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 23.4038ms
2025-07-18 14:55:59.085 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-18 14:55:59.085 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-18 14:55:59.089 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 45.0985ms
2025-07-18 14:55:59.090 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 45.8989ms
2025-07-18 14:55:59.106 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-18 14:55:59.108 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 63.9198ms
2025-07-18 14:55:59.668 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-18 14:55:59.689 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/favicon-32x32.png - null null
2025-07-18 14:55:59.694 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-07-18 14:55:59.697 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/favicon-32x32.png - 200 628 image/png 8.1956ms
2025-07-18 14:55:59.780 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 111.6766ms
2025-07-18 14:56:10.856 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 161
2025-07-18 14:56:10.867 +03:00 [INF] CORS policy execution successful.
2025-07-18 14:56:10.872 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 14:56:10.921 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 14:56:11.404 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 14:56:21.367 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 9950.9592ms.
2025-07-18 14:56:21.371 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 14:56:21.375 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 10445.0639ms
2025-07-18 14:56:21.376 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 14:56:21.376 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 10520.1433ms
2025-07-18 14:56:37.090 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 14:56:37.092 +03:00 [INF] COM ports populated.
2025-07-18 14:56:37.093 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 14:56:37.095 +03:00 [INF] TabPage returned successfully.
2025-07-18 14:56:48.353 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 14:56:48.359 +03:00 [INF] COM ports populated.
2025-07-18 14:56:48.365 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 14:56:48.370 +03:00 [INF] TabPage returned successfully.
2025-07-18 14:58:02.539 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 14:58:02.543 +03:00 [INF] COM ports populated.
2025-07-18 14:58:02.550 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 14:58:02.557 +03:00 [INF] TabPage returned successfully.
2025-07-18 14:58:50.238 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 14:58:50.241 +03:00 [INF] COM ports populated.
2025-07-18 14:58:50.247 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 14:58:50.251 +03:00 [INF] TabPage returned successfully.
2025-07-18 14:59:43.379 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 14:59:43.418 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 14:59:43.422 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 14:59:43.424 +03:00 [INF] Capture image mode set to: false
2025-07-18 14:59:43.425 +03:00 [INF] Initializing hub connection...
2025-07-18 14:59:45.179 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 14:59:45.260 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 14:59:45.267 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 14:59:45.277 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 14:59:45.282 +03:00 [INF] Hosting environment: Production
2025-07-18 14:59:45.287 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 14:59:45.791 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 14:59:45.833 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 14:59:45.864 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 14:59:45.874 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 87.2833ms
2025-07-18 14:59:48.054 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=yq4uCfWppFTz0PQJOZ8NWQ - null null
2025-07-18 14:59:48.072 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 14:59:48.167 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 14:59:48.238 +03:00 [INF] Barcode initialized successfully.
2025-07-18 14:59:55.605 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 14:59:55.611 +03:00 [INF] COM ports populated.
2025-07-18 14:59:55.616 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 14:59:55.617 +03:00 [INF] TabPage returned successfully.
2025-07-18 15:00:26.207 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 161
2025-07-18 15:00:26.222 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:00:26.226 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:00:26.254 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:00:26.691 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:00:28.003 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1303.8353ms.
2025-07-18 15:00:28.009 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:00:28.013 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1749.5071ms
2025-07-18 15:00:28.014 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:00:28.015 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 1808.5288ms
2025-07-18 15:00:43.960 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 161
2025-07-18 15:00:43.973 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:00:43.978 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:00:43.983 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:00:44.018 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:01:08.365 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 15:01:08.415 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 15:01:08.421 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 15:01:08.425 +03:00 [INF] Capture image mode set to: false
2025-07-18 15:01:08.427 +03:00 [INF] Initializing hub connection...
2025-07-18 15:01:10.224 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 15:01:10.301 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 15:01:10.306 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 15:01:10.312 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:01:10.314 +03:00 [INF] Hosting environment: Production
2025-07-18 15:01:10.316 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 15:01:10.809 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 15:01:10.842 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 15:01:10.863 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 15:01:10.894 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 90.4088ms
2025-07-18 15:01:13.036 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=C4lJmb1kUQh9_tN0cQMWOg - null null
2025-07-18 15:01:13.050 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 15:01:13.119 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 15:01:13.217 +03:00 [INF] Barcode initialized successfully.
2025-07-18 15:03:23.985 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 15:03:23.990 +03:00 [INF] COM ports populated.
2025-07-18 15:03:23.991 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 15:03:23.993 +03:00 [INF] TabPage returned successfully.
2025-07-18 15:03:46.338 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:03:46.361 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:03:46.368 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:03:46.405 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:03:46.861 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:06:45.448 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 15:06:45.481 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 15:06:45.485 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 15:06:45.488 +03:00 [INF] Capture image mode set to: false
2025-07-18 15:06:45.489 +03:00 [INF] Initializing hub connection...
2025-07-18 15:06:47.104 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 15:06:47.185 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 15:06:47.191 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 15:06:47.198 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:06:47.203 +03:00 [INF] Hosting environment: Production
2025-07-18 15:06:47.208 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 15:06:47.863 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 15:06:47.903 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 15:06:47.934 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 15:06:47.939 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 82.1623ms
2025-07-18 15:06:50.060 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=VAUmNaQOZQyGjlBGA2rcwg - null null
2025-07-18 15:06:50.075 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 15:06:50.166 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 15:06:50.240 +03:00 [INF] Barcode initialized successfully.
2025-07-18 15:06:57.634 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 15:06:57.640 +03:00 [INF] COM ports populated.
2025-07-18 15:06:57.647 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 15:06:57.654 +03:00 [INF] TabPage returned successfully.
2025-07-18 15:07:21.961 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 15:07:21.998 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 15:07:22.002 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 15:07:22.005 +03:00 [INF] Capture image mode set to: false
2025-07-18 15:07:22.007 +03:00 [INF] Initializing hub connection...
2025-07-18 15:07:23.768 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 15:07:23.838 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 15:07:23.841 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 15:07:23.845 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:07:23.846 +03:00 [INF] Hosting environment: Production
2025-07-18 15:07:23.847 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 15:07:24.362 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 15:07:24.402 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 15:07:24.429 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 15:07:24.472 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 84.6613ms
2025-07-18 15:07:26.599 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=9FS50WcvlWGtp8SwJ8ch5g - null null
2025-07-18 15:07:26.620 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 15:07:26.702 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 15:07:26.755 +03:00 [INF] Barcode initialized successfully.
2025-07-18 15:07:34.182 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 15:07:34.186 +03:00 [INF] COM ports populated.
2025-07-18 15:07:34.189 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 15:07:34.190 +03:00 [INF] TabPage returned successfully.
2025-07-18 15:08:06.756 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:08:06.773 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:08:06.779 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:08:06.808 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:08:07.193 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:10:01.973 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 15:10:02.017 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 15:10:02.021 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 15:10:02.024 +03:00 [INF] Capture image mode set to: false
2025-07-18 15:10:02.025 +03:00 [INF] Initializing hub connection...
2025-07-18 15:10:03.951 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 15:10:04.044 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 15:10:04.050 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 15:10:04.056 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:10:04.061 +03:00 [INF] Hosting environment: Production
2025-07-18 15:10:04.067 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 15:10:04.388 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 15:10:04.424 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 15:10:04.455 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 15:10:04.464 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 81.5763ms
2025-07-18 15:10:06.596 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=-qoA2Ct9-KNlG1Xuh-KUuw - null null
2025-07-18 15:10:06.617 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 15:10:06.729 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 15:10:06.812 +03:00 [INF] Barcode initialized successfully.
2025-07-18 15:10:15.122 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 15:10:15.124 +03:00 [INF] COM ports populated.
2025-07-18 15:10:15.129 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 15:10:15.132 +03:00 [INF] TabPage returned successfully.
2025-07-18 15:10:33.369 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:10:33.385 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:10:33.390 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:10:33.418 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:10:33.838 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:12:22.123 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 15:12:22.158 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 15:12:22.162 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 15:12:22.166 +03:00 [INF] Capture image mode set to: false
2025-07-18 15:12:22.167 +03:00 [INF] Initializing hub connection...
2025-07-18 15:12:23.899 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 15:12:23.972 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 15:12:23.976 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 15:12:23.980 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:12:23.985 +03:00 [INF] Hosting environment: Production
2025-07-18 15:12:23.990 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 15:12:24.552 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 15:12:24.586 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 15:12:24.606 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 15:12:24.609 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 61.6948ms
2025-07-18 15:12:33.872 +03:00 [INF] Starting Barcode Initialization...
2025-07-18 15:12:33.908 +03:00 [INF] Attempting to initialize barcode with URL: http://localhost:5000/chatHub and COM Port: COM3
2025-07-18 15:12:33.913 +03:00 [INF] PublicInitialize called with URL: http://localhost:5000/chatHub, COM Port: COM3, CaptureMode: false
2025-07-18 15:12:33.917 +03:00 [INF] Capture image mode set to: false
2025-07-18 15:12:33.918 +03:00 [INF] Initializing hub connection...
2025-07-18 15:12:35.639 +03:00 [WRN] The WebRootPath was not found: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows\wwwroot. Static files may be unavailable.
2025-07-18 15:12:35.711 +03:00 [INF] Now listening on: https://127.0.0.1:7000
2025-07-18 15:12:35.714 +03:00 [INF] Now listening on: http://127.0.0.1:5000
2025-07-18 15:12:35.719 +03:00 [INF] Application started. Press Ctrl+C to shut down.
2025-07-18 15:12:35.721 +03:00 [INF] Hosting environment: Production
2025-07-18 15:12:35.723 +03:00 [INF] Content root path: C:\Mis Agent\Mis.Agent\bin\Debug\net9.0-windows
2025-07-18 15:12:36.263 +03:00 [INF] Request starting HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - null 0
2025-07-18 15:12:36.301 +03:00 [INF] Executing endpoint '/chatHub/negotiate'
2025-07-18 15:12:36.335 +03:00 [INF] Executed endpoint '/chatHub/negotiate'
2025-07-18 15:12:36.341 +03:00 [INF] Request finished HTTP/1.1 POST http://localhost:5000/chatHub/negotiate?negotiateVersion=1 - 200 316 application/json 84.1784ms
2025-07-18 15:12:38.491 +03:00 [INF] Request starting HTTP/1.1 GET http://localhost:5000/chatHub?id=z6a9iavFt0UbLR4nfk3Unw - null null
2025-07-18 15:12:38.508 +03:00 [INF] Executing endpoint '/chatHub'
2025-07-18 15:12:38.595 +03:00 [INF] Hub connection successful. Starting to listen on COM port: COM3
2025-07-18 15:12:38.669 +03:00 [INF] Barcode initialized successfully.
2025-07-18 15:12:44.864 +03:00 [INF] Building Barcode TabPage UI.
2025-07-18 15:12:44.870 +03:00 [INF] COM ports populated.
2025-07-18 15:12:44.879 +03:00 [INF] Barcode scanner port set to: COM3
2025-07-18 15:12:44.886 +03:00 [INF] TabPage returned successfully.
2025-07-18 15:13:05.438 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:13:05.458 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:13:05.465 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:13:05.497 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:13:05.968 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:13:23.267 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 17285.1252ms.
2025-07-18 15:13:23.283 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:13:23.294 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 17775.8394ms
2025-07-18 15:13:23.295 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:13:23.301 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 17863.2479ms
2025-07-18 15:13:38.227 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:13:38.241 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:13:38.245 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:13:38.247 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:13:38.266 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:13:41.415 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 3143.8975ms.
2025-07-18 15:13:41.417 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:13:41.418 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 3168.3608ms
2025-07-18 15:13:41.419 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:13:41.421 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 3193.9051ms
2025-07-18 15:13:51.452 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:13:51.463 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:13:51.467 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:13:51.471 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:13:51.494 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:13:52.688 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1186.1818ms.
2025-07-18 15:13:52.692 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:13:52.693 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1220.3778ms
2025-07-18 15:13:52.695 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:13:52.697 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 1245.3259ms
2025-07-18 15:14:00.068 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:14:00.078 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:00.081 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:00.086 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:00.108 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:14:01.232 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1119.8236ms.
2025-07-18 15:14:01.234 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:14:01.236 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1139.9432ms
2025-07-18 15:14:01.237 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:01.238 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 1170.0915ms
2025-07-18 15:14:10.225 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 156
2025-07-18 15:14:10.234 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:10.237 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:10.240 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:10.262 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:14:11.492 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1221.8708ms.
2025-07-18 15:14:11.494 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:14:11.496 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1250.0813ms
2025-07-18 15:14:11.497 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:11.499 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 1274.075ms
2025-07-18 15:14:14.261 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:14:14.272 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:14.275 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:14.277 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:14.289 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:14:14.320 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 39.9704ms
2025-07-18 15:14:14.323 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:14.326 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 65.2245ms
2025-07-18 15:14:15.920 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:14:15.931 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:15.935 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:15.942 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:15.953 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:14:15.959 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 7.9836ms
2025-07-18 15:14:15.961 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:15.964 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 43.3404ms
2025-07-18 15:14:19.049 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:14:19.060 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:19.062 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:19.066 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:19.079 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:14:19.082 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 6.5143ms
2025-07-18 15:14:19.085 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:19.087 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 38.4837ms
2025-07-18 15:14:35.913 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:14:35.924 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:35.928 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:35.934 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:35.940 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:14:35.942 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 2.9102ms
2025-07-18 15:14:35.943 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:35.945 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 31.4178ms
2025-07-18 15:14:37.217 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:14:37.229 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:14:37.232 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:37.239 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:14:37.245 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:14:37.250 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 6.7509ms
2025-07-18 15:14:37.253 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:14:37.255 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 37.6086ms
2025-07-18 15:15:46.027 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:15:46.036 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:15:46.040 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:15:46.048 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:15:46.064 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:15:46.075 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 13.1828ms
2025-07-18 15:15:46.081 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:15:46.085 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 57.5485ms
2025-07-18 15:15:57.850 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 157
2025-07-18 15:15:57.862 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:15:57.866 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:15:57.872 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:15:57.885 +03:00 [INF] Executing BadRequestObjectResult, writing value of type 'Microsoft.AspNetCore.Mvc.ValidationProblemDetails'.
2025-07-18 15:15:57.894 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 10.3805ms
2025-07-18 15:15:57.898 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:15:57.900 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 400 null application/problem+json; charset=utf-8 49.2965ms
2025-07-18 15:16:01.029 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.html - null null
2025-07-18 15:16:01.094 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.html - 200 null text/html;charset=utf-8 65.7163ms
2025-07-18 15:16:01.148 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - null null
2025-07-18 15:16:01.148 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.css - null null
2025-07-18 15:16:01.150 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - null null
2025-07-18 15:16:01.197 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/index.js - null null
2025-07-18 15:16:01.196 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - null null
2025-07-18 15:16:01.226 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.js - 200 null application/javascript;charset=utf-8 29.0888ms
2025-07-18 15:16:01.227 +03:00 [INF] Sending file. Request path: '/index.css'. Physical path: 'N/A'
2025-07-18 15:16:01.233 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/index.css - 200 202 text/css 85.3132ms
2025-07-18 15:16:01.244 +03:00 [INF] Sending file. Request path: '/swagger-ui.css'. Physical path: 'N/A'
2025-07-18 15:16:01.244 +03:00 [INF] Sending file. Request path: '/swagger-ui-standalone-preset.js'. Physical path: 'N/A'
2025-07-18 15:16:01.247 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-standalone-preset.js - 200 230293 text/javascript 51.0301ms
2025-07-18 15:16:01.247 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui.css - 200 152034 text/css 99.2497ms
2025-07-18 15:16:01.249 +03:00 [INF] Sending file. Request path: '/swagger-ui-bundle.js'. Physical path: 'N/A'
2025-07-18 15:16:01.256 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger-ui-bundle.js - 200 1452753 text/javascript 105.7446ms
2025-07-18 15:16:01.601 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - null null
2025-07-18 15:16:01.602 +03:00 [INF] Request starting HTTP/2 GET https://127.0.0.1:7000/favicon-32x32.png - null null
2025-07-18 15:16:01.617 +03:00 [INF] Sending file. Request path: '/favicon-32x32.png'. Physical path: 'N/A'
2025-07-18 15:16:01.618 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/favicon-32x32.png - 200 628 image/png 16.1431ms
2025-07-18 15:16:01.707 +03:00 [INF] Request finished HTTP/2 GET https://127.0.0.1:7000/swagger/v1/swagger.json - 200 null application/json;charset=utf-8 106.1734ms
2025-07-18 15:16:28.523 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 165
2025-07-18 15:16:28.535 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:16:28.539 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:16:28.545 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:16:28.573 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:16:29.985 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1402.8ms.
2025-07-18 15:16:29.986 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:16:29.987 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1437.4701ms
2025-07-18 15:16:29.988 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:16:29.988 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 1465.2163ms
2025-07-18 15:16:42.256 +03:00 [INF] Request starting HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - application/json 158
2025-07-18 15:16:42.269 +03:00 [INF] CORS policy execution successful.
2025-07-18 15:16:42.274 +03:00 [INF] Executing endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:16:42.282 +03:00 [INF] Route matched with {action = "Print", controller = "Print"}. Executing controller action with signature System.Threading.Tasks.Task`1[Microsoft.AspNetCore.Mvc.ActionResult] PrintAsync(Mis.Shared.Interface.TransactionDto) on controller Mis.Agent.Print.PrintController (Mis.Agent.Print).
2025-07-18 15:16:42.318 +03:00 [INF] Executing action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) - Validation state: "Valid"
2025-07-18 15:16:43.705 +03:00 [INF] Executed action method Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print), returned result Microsoft.AspNetCore.Mvc.ObjectResult in 1380.535ms.
2025-07-18 15:16:43.714 +03:00 [INF] Executing ObjectResult, writing value of type 'System.String'.
2025-07-18 15:16:43.717 +03:00 [INF] Executed action Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print) in 1421.0919ms
2025-07-18 15:16:43.719 +03:00 [INF] Executed endpoint 'Mis.Agent.Print.PrintController.PrintAsync (Mis.Agent.Print)'
2025-07-18 15:16:43.720 +03:00 [INF] Request finished HTTP/2 POST https://127.0.0.1:7000/Print/PrintAsync - 500 null text/plain; charset=utf-8 1464.397ms
