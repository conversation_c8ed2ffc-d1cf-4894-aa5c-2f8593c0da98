<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing"
  mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework
  object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema"
    xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0,
      Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0,
      Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="PrintFormTitle" xml:space="preserve">
    <value>إعدادات الطباعة</value>
  </data>
  <data name="NotificationsFormTitle" xml:space="preserve">
    <value>إدارة الإشعارات</value>
  </data>
  <data name="PrintTabText" xml:space="preserve">
    <value>إعدادات الطباعة</value>
  </data>
  <data name="NotificationsTabText" xml:space="preserve">
    <value>الإشعارات</value>
  </data>
  <data name="PrinterNameLabel" xml:space="preserve">
    <value>اسم الطابعة:</value>
  </data>
  <data name="PaperSizeLabel" xml:space="preserve">
    <value>حجم الورق:</value>
  </data>
  <data name="DefaultPrinterLabel" xml:space="preserve">
    <value>الطابعة الافتراضية:</value>
  </data>
  <data name="CurrentPrinterLabel" xml:space="preserve">
    <value>الطابعة الحالية:</value>
  </data>
  <data name="CurrentPaperSizeLabel" xml:space="preserve">
    <value>حجم الورق الحالي:</value>
  </data>
  <data name="SavePrintConfigButton" xml:space="preserve">
    <value>حفظ إعدادات الطباعة</value>
  </data>
  <data name="TestPrintButton" xml:space="preserve">
    <value>طباعة تجريبية</value>
  </data>
  <data name="ClearNotificationsButton" xml:space="preserve">
    <value>مسح جميع الإشعارات</value>
  </data>
  <data name="EnableNotificationsCheckbox" xml:space="preserve">
    <value>تفعيل/إلغاء الإشعارات</value>
  </data>
  <data name="NotificationIdColumn" xml:space="preserve">
    <value>المعرف</value>
  </data>
  <data name="NotificationNumberColumn" xml:space="preserve">
    <value>الرقم</value>
  </data>
  <data name="IsPrintedColumn" xml:space="preserve">
    <value>مطبوع</value>
  </data>
  <data name="ReceiveTimeColumn" xml:space="preserve">
    <value>وقت الاستلام</value>
  </data>
  <data name="PrintConfigSavedMessage" xml:space="preserve">
    <value>تم حفظ إعدادات الطباعة بنجاح!</value>
  </data>
  <data name="PrintConfigFailedMessage" xml:space="preserve">
    <value>فشل في حفظ إعدادات الطباعة.</value>
  </data>
  <data name="TestPrintSuccessMessage" xml:space="preserve">
    <value>تمت الطباعة التجريبية بنجاح!</value>
  </data>
  <data name="TestPrintFailedMessage" xml:space="preserve">
    <value>فشلت الطباعة التجريبية. يرجى التحقق من إعدادات الطابعة.</value>
  </data>
  <data name="NotificationsClearedMessage" xml:space="preserve">
    <value>تم مسح جميع الإشعارات بنجاح!</value>
  </data>
  <data name="ClearNotificationsFailedMessage" xml:space="preserve">
    <value>فشل في مسح الإشعارات.</value>
  </data>
  <data name="ClearNotificationsConfirmMessage" xml:space="preserve">
    <value>هل أنت متأكد من أنك تريد مسح جميع الإشعارات؟</value>
  </data>
  <data name="NotificationsEnabledMessage" xml:space="preserve">
    <value>تم تفعيل الإشعارات.</value>
  </data>
  <data name="NotificationsDisabledMessage" xml:space="preserve">
    <value>تم إلغاء الإشعارات.</value>
  </data>
  <data name="NoPrinterSelectedMessage" xml:space="preserve">
    <value>يرجى اختيار طابعة.</value>
  </data>
  <data name="PrinterNotFoundMessage" xml:space="preserve">
    <value>لم يتم العثور على الطابعة المحددة.</value>
  </data>
  <data name="Properties" xml:space="preserve">
    <value>خصائص</value>
  </data>
  <data name="PaperType" xml:space="preserve">
    <value>نوع الورق</value>
  </data>
  <data name="PrintingError" xml:space="preserve">
    <value>خطأ طباعة</value>
  </data>
  <data name="HTMLContent" xml:space="preserve">
    <value>محتوى الصفحة التي تريد طباعتها فارغ</value>
  </data>
  <data name="PrintError" xml:space="preserve">
    <value>خطأ في الطباعة</value>
  </data>
  <data name="NoPrinterSelected" xml:space="preserve">
    <value>لم يتم اختيار طابعة</value>
  </data>
  <data name="PaperSizeNotAvailable" xml:space="preserve">
    <value>حجم الورق المحدد غير متوفر للطابعة المختارة</value>
  </data>
  <data name="NoPaperSizeSelected" xml:space="preserve">
    <value>لم يتم اختيار حجم الورق</value>
  </data>
  <data name="PrinterPropertiesFailed" xml:space="preserve">
    <value>فشل في عرض خصائص الطابعة</value>
  </data>
  <data name="PrinterPropertiesError" xml:space="preserve">
    <value>خطأ في عرض خصائص الطابعة</value>
  </data>
</root>