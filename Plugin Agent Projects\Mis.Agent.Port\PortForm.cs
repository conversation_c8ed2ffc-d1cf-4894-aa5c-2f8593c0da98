﻿using Mis.Shared.Interface;
using System.Diagnostics;
using System.Reflection;
using System.Reflection.Emit;
using System.Windows.Forms;
using System.Xml.Linq;
using static System.Windows.Forms.VisualStyles.VisualStyleElement;
using ToolTip = System.Windows.Forms.ToolTip;
using Mis.Agent.Port.Resources;

namespace Mis.Agent.Port
{
    public partial class PortForm : Form
    {
        private IPortAppService _portAppService;
        private ToolTip toolTip1 = new ToolTip();

        public PortForm(IPortAppService portAppService)
        {
            _portAppService = portAppService;
            InitializeComponent();
            AgentUrlField.Text = _portAppService.GetBaseUrl();
            InitializeLocalization();
            FillTextFromConfiguration();

        }

        private void FillTextFromConfiguration()
        {
            var barcodeUrl = _portAppService.GetBaseUrl();

            if (Uri.TryCreate(barcodeUrl, UriKind.Absolute, out Uri uri))
            {
                // ✅ Set protocol ("http" or "https")
                AgentProtocolField.SelectedItem = uri.Scheme;

                // ✅ Set host (IP or domain)
                AgentIpField.Text = uri.Host;

                // ✅ Set port
                AgentPortField.Text = uri.Port.ToString();

                // ✅ Set full URL in read-only textbox
                AgentUrlField.Text = uri.ToString();
                AgentUrlField.BackColor = Color.White;
            }
            else
            {
                // ❌ Show fallback if barcodeUrl is malformed
                AgentUrlField.Text = "";
                AgentUrlField.BackColor = Color.LightPink;
                toolTip1.SetToolTip(AgentUrlField, "الرابط المخزن في الإعدادات غير صالح");
            }
        }
        private void InitializeLocalization()
        {
            // Subscribe to culture changes via NotificationManager
            // Note: Culture changes will be handled by the main application

            // Apply initial localization
            ApplyLocalization();
        }

        public void OnCultureChanged()
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged()));
                return;
            }

            ApplyLocalization();
        }


        private void ApplyLocalization()
        {
            // Update form title
            this.Text = PortResources.PortFormTitle;

            // Update tab text
            PortTab.Text = PortResources.PortTabText;

            // Update labels
            label1.Text = PortResources.AgentPortLabel;
            label2.Text = PortResources.AgentIpLabel;
            label3.Text = PortResources.AgentUrlLabel;
            label4.Text = PortResources.AgentProtocolLabel;

            // Update buttons
        }

        private void UpdatePortUrl()
        {
            string ip = AgentIpField.Text.Trim();
            string portText = AgentPortField.Text.Trim();
            string protocol = AgentProtocolField.SelectedItem?.ToString()?.ToLower();

            bool isPortValid = int.TryParse(portText, out int port) && port > 0 && port <= 65535;

            if (string.IsNullOrWhiteSpace(protocol))
            {
                AgentUrlField.Text = "";
                AgentUrlField.BackColor = Color.LightPink;
                toolTip1.SetToolTip(AgentUrlField, "Please select protocol: http or https.");
                return;
            }

            if (!string.IsNullOrWhiteSpace(ip) && isPortValid)
            {
                string finalUrl = $"{protocol}://{ip}:{port}";

                if (Uri.TryCreate(finalUrl, UriKind.Absolute, out Uri uri))
                {
                    AgentUrlField.Text = uri.ToString();
                    AgentUrlField.BackColor = Color.White;
                }
                else
                {
                    AgentUrlField.BackColor = Color.LightYellow;
                    toolTip1.SetToolTip(AgentUrlField, "Invalid URL format. Check IP and port.");
                }
            }
            else
            {
                AgentUrlField.Text = "";
                AgentUrlField.BackColor = Color.LightPink;
                toolTip1.SetToolTip(AgentUrlField, "Enter valid IP and port.");
            }
        }

        private void AgentIpField_TextChanged(object sender, EventArgs e)
        {
            UpdatePortUrl();
        }

        private void PortTextBox_TextChanged(object sender, EventArgs e)
        {
            UpdatePortUrl();
        }

        private void AgentProtocolField_SelectedIndexChanged(object sender, EventArgs e)
        {
            UpdatePortUrl();
        }
    }




}
