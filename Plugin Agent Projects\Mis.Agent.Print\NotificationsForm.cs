﻿using Mis.Shared.Interface;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Mis.Agent.Print
{
    public partial class NotificationsForm : Form
    {
        private DataTable? _notificationsTable;
        INotificationAppService _notificationService;

        public NotificationsForm(INotificationAppService notificationService)
        {
            _notificationService = notificationService;
            _notificationsTable = new DataTable();
            InitializeComponent();
            InitializeLocalization();
            LoadNotificationSettings();

        }
   
        // دالة لتحميل الإعدادات (يمكن تعديلها حسب كيفية جلب الإعدادات)
        public void LoadNotificationSettings()
        {
           var _currentSettings = _notificationService.GetNotificationSettingsAsync().Result;

            // تعيين قيم الحقول بناءً على الإعدادات
            enableNotificationsCheckBox.Checked = _currentSettings.EnabeledNotification ?? false;

            if (_currentSettings.NotificationCleanup != null)
            {
                enableCleanupCheckBox.Checked = _currentSettings.NotificationCleanup.Enabled;
                txtKeepDays.Text = _currentSettings.NotificationCleanup.KeepDays?.ToString() ?? "";
                txtMaxRecords.Text = _currentSettings.NotificationCleanup.MaxRecords?.ToString() ?? "";
            }
            else
            {
                enableCleanupCheckBox.Checked = false;
                txtKeepDays.Text = "";
                txtMaxRecords.Text = "";
            }
        }
        private void InitializeLocalization()
        {
            // Subscribe to culture changes
            SimpleLocalization.CultureChanged += OnCultureChanged;

            // Apply initial localization
            ApplyLocalization();
        }

        private void OnCultureChanged(object sender, CultureChangedEventArgs e)
        {
            if (InvokeRequired)
            {
                Invoke(new Action(() => OnCultureChanged(sender, e)));
                return;
            }

            ApplyLocalization();
        }

        private void ApplyLocalization()
        {
            // Update form title
            this.Text = SimpleLocalization.GetString("NotificationsFormTitle", "Notifications Management");

            // Update tab text
            NotificationsTab.Text = SimpleLocalization.GetString("NotificationsTabText", "Notifications");

            // Update checkboxes
            enableNotificationsCheckBox.Text = SimpleLocalization.GetString("EnableNotificationsCheckbox", "Enable/Disable Notifications");

            // Update buttons
           // btnClearNotifications.Text = SimpleLocalization.GetString("ClearNotificationsButton", "Clear All Notifications");

            // Update column headers if DataGridView is initialized
            if (dataGridView1.Columns.Count > 0)
            {
                UpdateDataGridViewHeaders();
            }
        }

        private void UpdateDataGridViewHeaders()
        {
            if (_notificationsTable != null && _notificationsTable.Columns.Count > 0)
            {
                // Update column headers
                if (_notificationsTable.Columns.Contains("Id"))
                    _notificationsTable.Columns["Id"].ColumnName = SimpleLocalization.GetString("NotificationIdColumn", "ID");
                if (_notificationsTable.Columns.Contains("No"))
                    _notificationsTable.Columns["No"].ColumnName = SimpleLocalization.GetString("NotificationNumberColumn", "Number");
                if (_notificationsTable.Columns.Contains("IsPrinted"))
                    _notificationsTable.Columns["IsPrinted"].ColumnName = SimpleLocalization.GetString("IsPrintedColumn", "Is Printed");
                if (_notificationsTable.Columns.Contains("ReceiveTime"))
                    _notificationsTable.Columns["ReceiveTime"].ColumnName = SimpleLocalization.GetString("ReceiveTimeColumn", "Receive Time");
            }
        }
        private void checkBox1_CheckedChanged(object sender, EventArgs e)
        {
            var args = new NotificationEventArgs
            {
                IsEnabled = enableNotificationsCheckBox.Checked
            };
            NotificationManager.Notify(args);
        }
        public async void PopulateForm()
        {
            try
            {
                if (_notificationsTable == null) return;

                // Clear the existing data
                _notificationsTable.Clear();

                // Ensure the DataTable has the correct columns
                if (_notificationsTable.Columns.Count == 0)
                {
                    _notificationsTable.Columns.Add("Id", typeof(Guid));
                    _notificationsTable.Columns.Add("No", typeof(string));
                    _notificationsTable.Columns.Add("IsPrinted", typeof(bool));
                    _notificationsTable.Columns.Add("ReceiveTime", typeof(DateTime));
                }

                // Fetch the latest notifications from the database
                var notifications = await _notificationService.GetAllNotificationsAsync();

                // Populate the DataTable with the fetched notifications
                foreach (var notification in notifications)
                {
                    _notificationsTable.Rows.Add(notification.Id, notification.No, notification.IsPrinted, notification.ReceiveTime);
                }

                // Refresh the DataGridView to display the updated data
                dataGridView1.DataSource = _notificationsTable;
                dataGridView1.Refresh();
            }
            catch (Exception ex)
            {
                string errorTitle = SimpleLocalization.GetString("Error", "Error");
                string errorMessage = SimpleLocalization.GetString("ErrorOccurred", "An error occurred") + ": " + ex.Message;
                MessageBox.Show(errorMessage, errorTitle, MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void checkBox2_CheckedChanged(object sender, EventArgs e)
        {
            txtKeepDays.ReadOnly = !enableCleanupCheckBox.Checked;
            txtMaxRecords.ReadOnly = !enableCleanupCheckBox.Checked;
        }

    


    }
}
